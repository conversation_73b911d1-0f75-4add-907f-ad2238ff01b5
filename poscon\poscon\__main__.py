#!/usr/bin/env python3
"""
POSCON (Position Consolidation) - Main Entry Point

A comprehensive tool for analyzing unexplained position differences across exchanges,
identifying systematic explanations through database transaction analysis, and 
generating detailed Excel reports.

Usage:
    python -m poscon [options]
    
Options:
    --config, -c    Path to configuration file (default: config.local.yml)
    --threshold, -t Threshold for flagging exchanges (default: from config)
    --output, -o    Output Excel file path (default: from config)
    --no-excel      Skip Excel generation (analysis only)
    --verbose, -v   Enable verbose logging
    --help, -h      Show this help message
"""

import sys
import argparse
import logging
from pathlib import Path
from datetime import datetime

from .analyzer import run_complete_analysis
from .db import load_config


def setup_logging(verbose: bool = False):
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(f'poscon_{datetime.now().strftime("%Y%m%d")}.log')
        ]
    )


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="POSCON - Position Consolidation Analysis and Reporting",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python -m poscon                                  # Run with default config
    python -m poscon -c my_config.yml -t 50000       # Custom config and threshold
    python -m poscon --no-excel -v                   # Analysis only, verbose output
    python -m poscon -o custom_report.xlsx           # Custom output file
        """
    )
    
    parser.add_argument(
        "--config", "-c",
        type=str,
        default="config.local.yml",
        help="Path to configuration file (default: config.local.yml)"
    )
    
    parser.add_argument(
        "--threshold", "-t",
        type=float,
        help="Threshold for flagging exchanges (overrides config)"
    )
    
    parser.add_argument(
        "--output", "-o",
        type=str,
        help="Output Excel file path (overrides config)"
    )
    
    parser.add_argument(
        "--no-excel",
        action="store_true",
        help="Skip Excel generation (analysis only)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    return parser.parse_args()


def validate_config(config_path: str) -> bool:
    """Validate configuration file exists and has required settings."""
    if not Path(config_path).exists():
        print(f"❌ Configuration file not found: {config_path}")
        print("Please create a configuration file based on config.example.yml")
        return False
    
    try:
        config = load_config(config_path)
        
        # Check required sections
        required_sections = ['input_csv_path', 'database']
        missing_sections = [section for section in required_sections if section not in config]
        
        if missing_sections:
            print(f"⚠️  Missing configuration sections: {missing_sections}")
            print("Some functionality may not work properly.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return False


def print_banner():
    """Print application banner."""
    banner = """
╔═══════════════════════════════════════════════════════════════════════════════╗
║                           POSCON - Position Consolidation                     ║
║                    Exchange Position Analysis & Reporting Tool                 ║
╚═══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def print_analysis_summary(results: dict):
    """Print a summary of analysis results."""
    print(f"\n{'='*80}")
    print("ANALYSIS RESULTS SUMMARY")
    print(f"{'='*80}")
    
    print(f"📊 Threshold Used: ${results.get('threshold', 'N/A'):,.0f}")
    print(f"🔍 Problematic Exchanges Found: {len(results.get('problematic_exchanges', []))}")
    
    # Show problematic exchanges
    if results.get('problematic_exchanges'):
        print(f"\n🚨 Exchanges Over Threshold:")
        for i, exchange in enumerate(results['problematic_exchanges'], 1):
            flagged_assets = len(results.get('flagged_assets', {}).get(exchange, []))
            print(f"   {i:2d}. {exchange:<20} ({flagged_assets} flagged assets)")
    
    # Show systematic explanations summary
    if results.get('explanations'):
        total_explanations = sum(len(assets) for assets in results['explanations'].values())
        total_adjustments = 0.0
        
        for exchange_explanations in results['explanations'].values():
            for explanation in exchange_explanations.values():
                total_adjustments += explanation.get('total_adjustment_usd', 0.0)
        
        print(f"\n💡 Systematic Explanations:")
        print(f"   Assets Analyzed: {total_explanations}")
        print(f"   Total Adjustments: ${total_adjustments:,.2f}")
    
    # Show Excel report info
    if results.get('excel_report_path'):
        print(f"\n📋 Excel Report Generated:")
        print(f"   File: {results['excel_report_path']}")
        
        # Count expected sheets
        exchange_details = results.get('exchange_details', {})
        sheet_count = 2  # Summary sheets
        
        for exchange, analysis_df in exchange_details.items():
            if not analysis_df.empty:
                date_columns = [col for col in analysis_df.columns 
                              if col not in ['Exchange', 'Asset', 'Total_Unexplained', 'Flagged']]
                sheet_count += len(date_columns)
        
        print(f"   Sheets: {sheet_count} (2 summary + {sheet_count-2} exchange details)")


def main():
    """Main application entry point."""
    try:
        # Parse arguments
        args = parse_arguments()
        
        # Setup logging
        setup_logging(args.verbose)
        logger = logging.getLogger(__name__)
        
        # Print banner
        print_banner()
        
        # Validate configuration
        if not validate_config(args.config):
            return 1
        
        # Load configuration to show settings
        config = load_config(args.config)
        
        print(f"📁 Configuration: {args.config}")
        print(f"📊 Data Source: {config.get('input_csv_path', 'Not specified')}")
        print(f"🗃️  Database: {'Configured' if config.get('database') else 'Not configured'}")
        print(f"💰 Threshold: ${args.threshold or config.get('processing', {}).get('threshold', 10000):,.0f}")
        
        if args.no_excel:
            print(f"📋 Excel Output: Disabled")
        else:
            output_path = args.output or config.get('output_excel_path', 'output/poscon_report.xlsx')
            print(f"📋 Excel Output: {output_path}")
        
        # Start analysis
        print(f"\n{'='*80}")
        print("STARTING POSCON ANALYSIS")
        print(f"{'='*80}")
        
        # Override config settings with command line arguments
        if args.output and not args.no_excel:
            config['output_excel_path'] = args.output
        
        # Run complete analysis
        results = run_complete_analysis(
            config_path=args.config,
            threshold=args.threshold,
            generate_excel=not args.no_excel
        )
        
        # Print summary
        print_analysis_summary(results)
        
        print(f"\n{'='*80}")
        print("✅ POSCON ANALYSIS COMPLETED SUCCESSFULLY")
        print(f"{'='*80}")
        
        logger.info("POSCON analysis completed successfully")
        return 0
        
    except KeyboardInterrupt:
        print(f"\n\n⚠️  Analysis interrupted by user")
        return 130
    
    except Exception as e:
        print(f"\n❌ Error during POSCON analysis: {e}")
        
        if args.verbose:
            import traceback
            traceback.print_exc()
        
        logging.getLogger(__name__).error(f"POSCON analysis failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
