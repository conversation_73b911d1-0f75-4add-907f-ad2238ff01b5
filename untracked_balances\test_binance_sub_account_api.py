#!/usr/bin/env python3
"""
End-to-end Binance script:
- Lists regular & managed sub-accounts
- Fetches spot & futures totals
- Outputs each sub-account's value in BOTH BTC and USDT (current prices)
"""

import time
import hmac
import hashlib
from decimal import Decimal, ROUND_DOWN
from urllib.parse import urlencode
import requests
import yaml
from collections import defaultdict

# ========= CONFIG =========
with open("config.yml", "r") as f:
    config = yaml.safe_load(f)

BASE_URL = "https://api.binance.com"
API_KEY = config["api_key"]
API_SECRET = config["api_secret"]

TIMEOUT = 15
PAGE_SIZE = 20          # spotSummary/futuresSummary max is 20
DEC_PLACES = Decimal("0.********")  # for nice rounding

# ========= UTILS =========
def now_ms() -> int:
    return int(time.time() * 1000)

def sign(params: dict) -> str:
    query = urlencode(params, doseq=True)
    sig = hmac.new(API_SECRET.encode(), query.encode(), hashlib.sha256).hexdigest()
    return f"{query}&signature={sig}"

def signed_get(path: str, params: dict) -> dict:
    params.setdefault("timestamp", now_ms())
    headers = {"X-MBX-APIKEY": API_KEY}
    url = f"{BASE_URL}{path}?{sign(params)}"
    r = requests.get(url, headers=headers, timeout=TIMEOUT)
    r.raise_for_status()
    return r.json()

def public_get(path: str, params: dict | None = None) -> dict | list:
    url = f"{BASE_URL}{path}"
    r = requests.get(url, params=params or {}, timeout=TIMEOUT)
    r.raise_for_status()
    return r.json()

# ========= PRICE HELPERS =========
_price_cache: dict[str, Decimal] = {}

def get_price(symbol: str) -> Decimal:
    """Latest price for a single symbol, cached."""
    if symbol not in _price_cache:
        data = public_get("/api/v3/ticker/price", {"symbol": symbol})
        _price_cache[symbol] = Decimal(data["price"])
    return _price_cache[symbol]

def price_to_usdt(asset: str) -> Decimal:
    """Return how many USDT 1 unit of `asset` is worth right now."""
    if asset in ("USDT", "USD", "BUSD"):  # Common stablecoins
        return Decimal(1)
    if asset == "BTC":
        return get_price("BTCUSDT")
    
    # Skip assets that already contain USDT to avoid LDUSDTUSDT-like pairs
    if "USDT" in asset:
        return Decimal(1)  # Treat as stablecoin
    
    # Try direct USDT pair first
    symbol = f"{asset}USDT"
    try:
        return get_price(symbol)
    except requests.HTTPError:
        # Fallback via BTC if it exists
        try:
            asset_btc = get_price(f"{asset}BTC")
            return asset_btc * get_price("BTCUSDT")
        except requests.HTTPError:
            # If no pairs exist, treat as worthless to avoid crashes
            print(f"Warning: No price found for {asset}, treating as 0")
            return Decimal(0)

def price_to_btc(asset: str) -> Decimal:
    """Return how many BTC 1 unit of `asset` is worth right now."""
    if asset == "BTC":
        return Decimal(1)
    if asset in ("USDT", "USD", "BUSD"):
        return Decimal(1) / get_price("BTCUSDT")
    
    # Skip assets that already contain BTC to avoid invalid pairs
    if "BTC" in asset:
        return Decimal(0)  # Conservative approach
    
    # Try direct BTC pair first
    symbol = f"{asset}BTC"
    try:
        return get_price(symbol)
    except requests.HTTPError:
        # Fallback via USDT if it exists
        try:
            asset_usdt = get_price(f"{asset}USDT")
            return asset_usdt / get_price("BTCUSDT")
        except requests.HTTPError:
            # If no pairs exist, treat as worthless to avoid crashes
            print(f"Warning: No price found for {asset}, treating as 0")
            return Decimal(0)

# ========= REGULAR SUB-ACCOUNTS =========
def get_regular_sub_accounts() -> list[str]:
    """Emails of regular sub-accounts."""
    emails = []
    page = 1
    while True:
        resp = signed_get("/sapi/v1/sub-account/list", {"page": page, "limit": 200})
        rows = resp.get("subAccounts", []) or resp.get("data", []) or resp
        if isinstance(rows, dict) and "subAccounts" in rows:
            rows = rows["subAccounts"]
        if not rows:
            break
        for item in rows:
            emails.append(item["email"])
        if len(rows) < 200:
            break
        page += 1
    return emails

def get_regular_spot_btc_totals():
    """Return dict[email] -> BTC total from /spotSummary."""
    totals = {}
    page = 1
    while True:
        data = signed_get("/sapi/v1/sub-account/spotSummary", {"page": page, "size": PAGE_SIZE})
        lst = data.get("spotSubUserAssetBtcVoList", [])
        for row in lst:
            totals[row["email"]] = Decimal(row["totalAsset"])
        if len(lst) < PAGE_SIZE:
            break
        page += 1
    return totals

def get_regular_futures_summaries():
    """
    Return two dicts keyed by email:
      usdtm_usd  : totals (USDT/BUSD) from futuresType=1
      coinm_btc  : totals (BTC units) from futuresType=2
    """
    def pull(ftype: int):
        out = {}
        page = 1
        key = "futureAccountSummaryResp" if ftype == 1 else "deliveryAccountSummaryResp"
        while True:
            data = signed_get("/sapi/v2/sub-account/futures/accountSummary", {"futuresType": ftype, "page": page, "limit": PAGE_SIZE})
            sublist = data.get(key, {}).get("subAccountList", [])
            for row in sublist:
                out[row["email"]] = Decimal(row.get("totalMarginBalance", row.get("totalWalletBalance", "0")))
            if len(sublist) < PAGE_SIZE:
                break
            page += 1
        return out

    usdtm = pull(1)
    coinm = pull(2)
    return usdtm, coinm

def get_regular_margin_btc_totals():
    """Return dict[email] -> BTC total from margin summary."""
    totals = {}
    
    try:
        # Call the margin summary endpoint without email parameter to get all accounts
        data = signed_get("/sapi/v1/sub-account/margin/accountSummary", {})
        
        # The response contains a subAccountList with individual account details
        sub_accounts = data.get("subAccountList", [])
        print(f"Found {len(sub_accounts)} accounts with margin data")
        
        for account in sub_accounts:
            email = account["email"]
            # Use totalAssetOfBtc (includes borrowed funds as available cash)
            total_btc = Decimal(account.get("totalAssetOfBtc", "0"))
            totals[email] = total_btc
            if total_btc > 0:
                net_btc = Decimal(account.get("totalNetAssetOfBtc", "0"))
                borrowed_btc = total_btc - net_btc
                print(f"  {email}: {total_btc} BTC total margin (net: {net_btc}, borrowed: {borrowed_btc})")
        
    except requests.HTTPError as e:
        # Some accounts may not have margin enabled
        print(f"Warning: Could not fetch margin data - {e}")
    
    return totals

# ========= MANAGED SUB-ACCOUNTS =========
def get_managed_sub_accounts() -> list[str]:
    emails = []
    page = 1
    while True:
        resp = signed_get("/sapi/v1/managed-subaccount/info", {"page": page, "limit": 20})
        lst = resp.get("managerSubUserInfoVoList", []) or []
        for r in lst:
            emails.append(r["email"])
        if len(lst) < 20:
            break
        page += 1
    return emails

def get_managed_spot_btc_total(email: str) -> Decimal:
    """Sum btcValue from /managed-subaccount/asset."""
    rows = signed_get("/sapi/v1/managed-subaccount/asset", {"email": email})
    total_btc = Decimal("0")
    for r in rows:
        total_btc += Decimal(r["btcValue"])
    return total_btc

def get_managed_futures_totals(email: str):
    """
    Returns (usdt_equiv, btc_equiv) for that managed sub, summing both UM and CM results.
    /managed-subaccount/fetch-future-asset
    """
    usdt_total = Decimal("0")
    btc_total = Decimal("0")

    for acct_type in (None, "USDT_FUTURE", "COIN_FUTURE"):
        params = {"email": email}
        if acct_type:
            params["accountType"] = acct_type
        data = signed_get("/sapi/v1/managed-subaccount/fetch-future-asset", params)
        # Structure contains snapshotVos[0]["data"]["assets"]
        for snap in data.get("snapshotVos", []):
            assets = snap.get("data", {}).get("assets", [])
            for a in assets:
                asset = a["asset"]
                bal = Decimal(str(a.get("marginBalance") or a.get("walletBalance") or "0"))
                usdt_total += bal * price_to_usdt(asset)
                btc_total += bal * price_to_btc(asset)

    # Deduplicate if snapshots repeat? Using latest only is safer—left as simple sum for demo.
    return usdt_total, btc_total

def get_managed_margin_totals(email: str):
    """
    Returns (usdt_equiv, btc_equiv) for managed sub-account margin.
    """
    usdt_total = Decimal("0")
    btc_total = Decimal("0")
    
    try:
        # Try to get margin asset data for managed sub-account
        data = signed_get("/sapi/v1/managed-subaccount/marginAsset", {"email": email})
        for asset_data in data:
            asset = asset_data["asset"]
            # Use netAsset or netAssetOfBtc if available, fallback to free + borrowed
            net_asset = Decimal(str(asset_data.get("netAsset", 
                                   asset_data.get("free", "0"))))
            
            usdt_total += net_asset * price_to_usdt(asset)
            btc_total += net_asset * price_to_btc(asset)
    except requests.HTTPError as e:
        # Margin may not be enabled for this managed sub-account
        print(f"Warning: Could not fetch margin data for {email} - {e}")
    
    return usdt_total, btc_total

# ========= MAIN AGGREGATION =========
def quant(x: Decimal) -> Decimal:
    return x.quantize(DEC_PLACES, rounding=ROUND_DOWN)

def main():
    btc_usdt = get_price("BTCUSDT")

    # Get the list of emails to filter from config
    filter_emails = set(config.get("emails", []))
    print(f"Filtering for {len(filter_emails)} configured emails...")
    
    results = []

    # Regular subs
    reg_emails = get_regular_sub_accounts()
    spot_btc = get_regular_spot_btc_totals()
    usdtm_usd, coinm_btc = get_regular_futures_summaries()
    margin_btc = get_regular_margin_btc_totals()

    for email in reg_emails:
        # Only process emails that are in the config filter list
        if not filter_emails or email in filter_emails:
            spot_btc_val = spot_btc.get(email, Decimal("0"))
            spot_usdt_val = spot_btc_val * btc_usdt

            fut_usdtm_usdt = Decimal(usdtm_usd.get(email, 0))
            fut_usdtm_btc = fut_usdtm_usdt / btc_usdt

            fut_coinm_btc = Decimal(coinm_btc.get(email, 0))
            fut_coinm_usdt = fut_coinm_btc * btc_usdt

            margin_btc_val = Decimal(margin_btc.get(email, 0))
            margin_usdt_val = margin_btc_val * btc_usdt

            total_btc = spot_btc_val + fut_usdtm_btc + fut_coinm_btc + margin_btc_val
            total_usdt = spot_usdt_val + fut_usdtm_usdt + fut_coinm_usdt + margin_usdt_val

            results.append({
                "email": email,
                "type": "regular",
                "spot_btc": quant(spot_btc_val),
                "spot_usdt": quant(spot_usdt_val),
                "margin_btc": quant(margin_btc_val),
                "margin_usdt": quant(margin_usdt_val),
                "fut_btc": quant(fut_usdtm_btc + fut_coinm_btc),
                "fut_usdt": quant(fut_usdtm_usdt + fut_coinm_usdt),
                "total_btc": quant(total_btc),
                "total_usdt": quant(total_usdt),
            })

    # Managed subs
    man_emails = get_managed_sub_accounts()
    for email in man_emails:
        # Only process emails that are in the config filter list
        if not filter_emails or email in filter_emails:
            s_btc = get_managed_spot_btc_total(email)
            s_usdt = s_btc * btc_usdt

            f_usdt, f_btc = get_managed_futures_totals(email)
            m_usdt, m_btc = get_managed_margin_totals(email)

            total_btc = s_btc + f_btc + m_btc
            total_usdt = s_usdt + f_usdt + m_usdt

            results.append({
                "email": email,
                "type": "managed",
                "spot_btc": quant(s_btc),
                "spot_usdt": quant(s_usdt),
                "margin_btc": quant(m_btc),
                "margin_usdt": quant(m_usdt),
                "fut_btc": quant(f_btc),
                "fut_usdt": quant(f_usdt),
                "total_btc": quant(total_btc),
                "total_usdt": quant(total_usdt),
            })

    # Print nice summary
    print("=" * 150)
    print("SUB-ACCOUNT BALANCE SUMMARY (SPOT + MARGIN + FUTURES)")
    print("=" * 150)
    
    # Header
    print(f"{'EMAIL':<50} {'TYPE':<8} {'SPOT BTC':<15} {'SPOT USDT':<15} {'MARGIN BTC':<15} {'MARGIN USDT':<15} {'FUT BTC':<15} {'FUT USDT':<15} {'TOTAL BTC':<15} {'TOTAL USDT':<15}")
    print("-" * 150)
    
    # Sort by total USDT value (descending)
    results.sort(key=lambda x: x['total_usdt'], reverse=True)
    
    grand_total_btc = Decimal(0)
    grand_total_usdt = Decimal(0)
    
    for row in results:
        # If filtering is enabled, show all filtered accounts (including zero balances)
        # If no filtering, only show accounts with non-zero balances
        show_account = filter_emails or row['total_btc'] > 0
        
        if show_account:
            print(f"{row['email']:<50} {row['type']:<8} {row['spot_btc']:<15} {row['spot_usdt']:<15} {row['margin_btc']:<15} {row['margin_usdt']:<15} {row['fut_btc']:<15} {row['fut_usdt']:<15} {row['total_btc']:<15} {row['total_usdt']:<15}")
            grand_total_btc += row['total_btc']
            grand_total_usdt += row['total_usdt']
    
    print("-" * 150)
    print(f"{'GRAND TOTAL':<50} {'':<8} {'':<15} {'':<15} {'':<15} {'':<15} {'':<15} {'':<15} {quant(grand_total_btc):<15} {quant(grand_total_usdt):<15}")
    print("=" * 150)
    
    # Summary stats
    if filter_emails:
        # When filtering, count all filtered accounts
        total_accounts = len(results)
        regular_accounts = len([r for r in results if r['type'] == 'regular'])
        managed_accounts = len([r for r in results if r['type'] == 'managed'])
    else:
        # When not filtering, count only accounts with balances
        total_accounts = len([r for r in results if r['total_btc'] > 0])
        regular_accounts = len([r for r in results if r['type'] == 'regular' and r['total_btc'] > 0])
        managed_accounts = len([r for r in results if r['type'] == 'managed' and r['total_btc'] > 0])
    
    print(f"\nSUMMARY:")
    print(f"  Active Accounts: {total_accounts} ({regular_accounts} regular + {managed_accounts} managed)")
    print(f"  Total Portfolio: {quant(grand_total_btc)} BTC ≈ ${quant(grand_total_usdt):,} USDT")
    print(f"  BTC Price: ${get_price('BTCUSDT'):,}")
    print(f"  Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S UTC', time.gmtime())}")
    print("=" * 150)

if __name__ == "__main__":
    main()