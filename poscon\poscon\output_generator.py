"""Excel report generation module for POSCON analysis."""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.utils import get_column_letter
from typing import Dict, Any, List
from datetime import datetime
import os


class PosconExcelGenerator:
    """Generate POSCON Excel reports with before/after adjustments and exchange details."""
    
    def __init__(self, output_path: str):
        self.output_path = output_path
        self.workbook = None
    
    def generate_poscon_report(self, analysis_results: Dict[str, Any]) -> str:
        """
        Generate comprehensive POSCON Excel report.
        
        Args:
            analysis_results: Results from run_deep_analysis()
            
        Returns:
            Path to generated Excel file
        """
        print(f"Generating Excel report: {self.output_path}")
        
        # Create workbook
        self.workbook = Workbook()
        
        # Remove default sheet
        if 'Sheet' in self.workbook.sheetnames:
            self.workbook.remove(self.workbook['Sheet'])
        
        # Extract data from results
        exchange_analysis = analysis_results.get('exchange_details', {})
        explanations = analysis_results.get('explanations', {})
        threshold = analysis_results.get('threshold', 10000)
        
        # Generate summary tables
        self._add_summary_before_adjustments(exchange_analysis, threshold)
        self._add_summary_after_adjustments(exchange_analysis, explanations, threshold)
        
        # Generate individual exchange sheets
        self._add_exchange_detail_sheets(exchange_analysis, explanations)
        
        # Save workbook
        self._ensure_output_directory()
        self.workbook.save(self.output_path)
        
        print(f"Excel report saved: {self.output_path}")
        return self.output_path
    
    def _add_summary_before_adjustments(self, exchange_analysis: Dict, threshold: float):
        """Add summary sheet showing unexplained amounts before adjustments."""
        ws = self.workbook.create_sheet("Summary - Before Adjustments")
        
        # Collect all exchanges and dates
        all_exchanges = []
        all_dates = set()
        
        for exchange, analysis_df in exchange_analysis.items():
            if not analysis_df.empty:
                all_exchanges.append(exchange)
                # Get date columns (excluding Exchange and helper columns)
                date_columns = [col for col in analysis_df.columns 
                              if col not in ['Exchange', 'Asset', 'Total_Unexplained', 'Flagged']]
                all_dates.update(date_columns)
        
        # Sort dates
        all_dates = sorted(list(all_dates))
        
        # Create pivot table structure
        pivot_data = []
        for exchange in all_exchanges:
            row_data = {'Exchange': exchange}
            analysis_df = exchange_analysis[exchange]
            
            for date_col in all_dates:
                if date_col in analysis_df.columns:
                    total_unexplained = analysis_df[date_col].sum()
                    row_data[str(date_col)] = total_unexplained
                else:
                    row_data[str(date_col)] = 0.0
            
            pivot_data.append(row_data)
        
        # Create DataFrame
        if pivot_data:
            summary_df = pd.DataFrame(pivot_data)
            # Reorder columns: Exchange first, then sorted dates
            columns = ['Exchange'] + [str(date) for date in all_dates]
            summary_df = summary_df[columns]
        else:
            summary_df = pd.DataFrame(columns=['Exchange'])
        
        # Add title
        ws['A1'] = "POSCON Analysis - Summary Before Adjustments"
        ws['A1'].font = Font(size=16, bold=True)
        ws['A2'] = f"Threshold: ${threshold:,.0f}"
        ws['A3'] = f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        # Add data starting from row 5
        if not summary_df.empty:
            for r_idx, row in enumerate(dataframe_to_rows(summary_df, index=False, header=True), start=5):
                for c_idx, value in enumerate(row, start=1):
                    cell = ws.cell(row=r_idx, column=c_idx, value=value)
                    if r_idx == 5:  # Header row
                        cell.font = Font(bold=True)
                        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                    elif isinstance(value, (int, float)) and c_idx > 1:  # Format numeric columns (excluding Exchange column)
                        cell.number_format = '#,##0.00'
                        # Highlight cells over threshold
                        if abs(value) > threshold:
                            cell.fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")
        
        self._format_worksheet(ws)
    
    def _add_summary_after_adjustments(self, exchange_analysis: Dict, explanations: Dict, threshold: float):
        """Add summary sheet showing unexplained amounts after adjustments."""
        ws = self.workbook.create_sheet("Summary - After Adjustments")
        
        # Collect all exchanges and dates
        all_exchanges = []
        all_dates = set()
        
        for exchange, analysis_df in exchange_analysis.items():
            if not analysis_df.empty:
                all_exchanges.append(exchange)
                # Get date columns (excluding Exchange and helper columns)
                date_columns = [col for col in analysis_df.columns 
                              if col not in ['Exchange', 'Asset', 'Total_Unexplained', 'Flagged']]
                all_dates.update(date_columns)
        
        # Sort dates
        all_dates = sorted(list(all_dates))
        
        # Create pivot table structure for adjusted amounts
        pivot_data = []
        for exchange in all_exchanges:
            row_data = {'Exchange': exchange}
            analysis_df = exchange_analysis[exchange]
            
            for date_col in all_dates:
                if date_col in analysis_df.columns:
                    original_unexplained = analysis_df[date_col].sum()
                    
                    # Calculate total adjustments for this exchange-date
                    total_adjustment = 0.0
                    if exchange in explanations:
                        for asset, explanation in explanations[exchange].items():
                            total_adjustment += explanation.get('total_adjustment_usd', 0.0)
                    
                    adjusted_unexplained = original_unexplained - total_adjustment
                    row_data[str(date_col)] = adjusted_unexplained
                else:
                    row_data[str(date_col)] = 0.0
            
            pivot_data.append(row_data)
        
        # Create DataFrame
        if pivot_data:
            summary_df = pd.DataFrame(pivot_data)
            # Reorder columns: Exchange first, then sorted dates
            columns = ['Exchange'] + [str(date) for date in all_dates]
            summary_df = summary_df[columns]
        else:
            summary_df = pd.DataFrame(columns=['Exchange'])
        
        # Add title
        ws['A1'] = "POSCON Analysis - Summary After Adjustments"
        ws['A1'].font = Font(size=16, bold=True)
        ws['A2'] = f"Threshold: ${threshold:,.0f}"
        ws['A3'] = f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        # Add data starting from row 5
        if not summary_df.empty:
            for r_idx, row in enumerate(dataframe_to_rows(summary_df, index=False, header=True), start=5):
                for c_idx, value in enumerate(row, start=1):
                    cell = ws.cell(row=r_idx, column=c_idx, value=value)
                    if r_idx == 5:  # Header row
                        cell.font = Font(bold=True)
                        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                    elif isinstance(value, (int, float)) and c_idx > 1:  # Format numeric columns (excluding Exchange column)
                        cell.number_format = '#,##0.00'
                        # Highlight cells still over threshold after adjustments
                        if abs(value) > threshold:
                            cell.fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")
                        # Highlight cells that were resolved (green)
                        elif abs(value) <= threshold and value != 0:
                            cell.fill = PatternFill(start_color="CCFFCC", end_color="CCFFCC", fill_type="solid")
        
        self._format_worksheet(ws)
    
    def _add_exchange_detail_sheets(self, exchange_analysis: Dict, explanations: Dict):
        """Add individual sheets for each exchange-date combination."""
        for exchange, analysis_df in exchange_analysis.items():
            if analysis_df.empty:
                continue
                
            # Get date columns
            date_columns = [col for col in analysis_df.columns 
                          if col not in ['Exchange', 'Asset', 'Total_Unexplained', 'Flagged']]
            
            for date_col in date_columns:
                # Create sheet name: exchangename-date
                sheet_name = f"{exchange}-{str(date_col)}"
                # Excel sheet names are limited to 31 characters
                if len(sheet_name) > 31:
                    sheet_name = f"{exchange[:20]}-{str(date_col)}"
                
                ws = self.workbook.create_sheet(sheet_name)
                
                # Add exchange-date header
                ws['A1'] = f"Exchange: {exchange}"
                ws['A1'].font = Font(size=14, bold=True)
                ws['A2'] = f"Date: {date_col}"
                ws['A2'].font = Font(size=12, bold=True)
                
                # Add asset-level data
                ws['A4'] = "Asset-Level Analysis"
                ws['A4'].font = Font(size=12, bold=True)
                
                # Prepare asset data for this date
                asset_data = analysis_df[['Asset', date_col]].copy()
                asset_data = asset_data.rename(columns={date_col: 'Unexplained_USD'})
                asset_data = asset_data.sort_values('Unexplained_USD', key=abs, ascending=False)
                
                # Add asset data starting from row 6
                for r_idx, row in enumerate(dataframe_to_rows(asset_data, index=False, header=True), start=6):
                    for c_idx, value in enumerate(row, start=1):
                        cell = ws.cell(row=r_idx, column=c_idx, value=value)
                        if r_idx == 6:  # Header row
                            cell.font = Font(bold=True)
                            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                        elif isinstance(value, (int, float)) and c_idx == 2:  # Format USD column
                            cell.number_format = '#,##0.00'
                
                # Add explanations if available
                if exchange in explanations:
                    current_row = len(asset_data) + 8
                    ws[f'A{current_row}'] = "Systematic Explanations"
                    ws[f'A{current_row}'].font = Font(size=12, bold=True)
                    current_row += 2
                    
                    for asset, explanation in explanations[exchange].items():
                        ws[f'A{current_row}'] = f"Asset: {asset}"
                        ws[f'A{current_row}'].font = Font(bold=True)
                        current_row += 1
                        
                        adjustments = explanation.get('adjustments', [])
                        total_adj = explanation.get('total_adjustment_usd', 0)
                        
                        ws[f'B{current_row}'] = f"Total Adjustment: ${total_adj:,.2f}"
                        current_row += 1
                        
                        for adj in adjustments:
                            ws[f'C{current_row}'] = f"• {adj['case']}: {adj['count']} transfers, ${adj['adjustment_usd']:,.2f}"
                            current_row += 1
                            ws[f'D{current_row}'] = f"  {adj['reason']}"
                            current_row += 1
                        
                        current_row += 1  # Add space between assets
                
                self._format_worksheet(ws)
    
    def _format_worksheet(self, worksheet):
        """Apply formatting to worksheet."""
        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
            worksheet.column_dimensions[column_letter].width = adjusted_width
        
        # Add borders to data cells
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        for row in worksheet.iter_rows():
            for cell in row:
                if cell.value is not None:
                    cell.border = thin_border
    
    def _ensure_output_directory(self):
        """Ensure output directory exists."""
        output_dir = os.path.dirname(self.output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir) 