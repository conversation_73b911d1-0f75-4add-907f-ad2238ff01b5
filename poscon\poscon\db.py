import pandas as pd
import yaml
import os
import glob
from datetime import datetime
from typing import List, Dict, Any, Optional


def load_config(config_path: str = "config.local.yml") -> Dict[str, Any]:
    """Load configuration from YAML file."""
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    with open(config_path, 'r') as file:
        config = yaml.safe_load(file)
    
    return config


def find_csv_files(csv_path: str) -> List[str]:
    """Find CSV files in the specified path."""
    if os.path.isfile(csv_path):
        return [csv_path]
    elif os.path.isdir(csv_path):
        return glob.glob(os.path.join(csv_path, "*.csv"))
    else:
        raise FileNotFoundError(f"CSV path not found: {csv_path}")


def convert_timestamp_to_date(df: pd.DataFrame, timestamp_col: str = "Start Timestamp") -> pd.DataFrame:
    """Convert timestamp column to date and replace the original column."""
    df = df.copy()
    
    # Convert timestamp (assuming it's in milliseconds) to datetime
    df[timestamp_col] = pd.to_datetime(df[timestamp_col], unit='ms')
    
    # Extract just the date part
    df['Date'] = df[timestamp_col].dt.date
    
    # Drop the original timestamp column
    df = df.drop(columns=[timestamp_col])
    
    return df


def filter_blacklisted_data(df: pd.DataFrame, blacklists: Dict[str, List[str]]) -> pd.DataFrame:
    """Remove rows with blacklisted exchanges and assets."""
    df_filtered = df.copy()
    
    # Filter out blacklisted exchanges
    if 'exchanges' in blacklists and blacklists['exchanges']:
        exchange_blacklist = blacklists['exchanges']
        initial_count = len(df_filtered)
        df_filtered = df_filtered[~df_filtered['Exchange'].isin(exchange_blacklist)]
        removed_exchanges = initial_count - len(df_filtered)
        print(f"Removed {removed_exchanges} rows with blacklisted exchanges: {exchange_blacklist}")
    
    # Filter out blacklisted assets
    if 'assets' in blacklists and blacklists['assets']:
        asset_blacklist = blacklists['assets']
        initial_count = len(df_filtered)
        df_filtered = df_filtered[~df_filtered['Asset'].isin(asset_blacklist)]
        removed_assets = initial_count - len(df_filtered)
        print(f"Removed {removed_assets} rows with blacklisted assets: {asset_blacklist}")
    
    return df_filtered


def load_and_process_data(config_path: str = "config.yml") -> pd.DataFrame:
    """
    Main function to load config, read CSV data, process timestamps, 
    and filter blacklisted data.
    
    Returns:
        pd.DataFrame: Processed dataframe ready for analysis
    """
    # Load configuration
    config = load_config(config_path)
    
    # Get input path from config
    input_path = config.get('input_csv_path', 'input/')
    
    # Find CSV files
    csv_files = find_csv_files(input_path)
    print(f"Found {len(csv_files)} CSV file(s): {csv_files}")
    
    # Read and combine all CSV files
    dataframes = []
    for csv_file in csv_files:
        print(f"Loading {csv_file}...")
        df = pd.read_csv(csv_file)
        dataframes.append(df)
    
    # Combine all dataframes
    if len(dataframes) == 1:
        combined_df = dataframes[0]
    else:
        combined_df = pd.concat(dataframes, ignore_index=True)
    
    print(f"Loaded {len(combined_df)} total rows from CSV file(s)")
    
    # Convert timestamp to date
    processed_df = convert_timestamp_to_date(combined_df)
    print("Converted timestamp to date")
    
    # Apply blacklist filters
    blacklists = config.get('blacklists', {})
    if blacklists:
        processed_df = filter_blacklisted_data(processed_df, blacklists)
    
    print(f"Final processed dataframe: {len(processed_df)} rows, {len(processed_df.columns)} columns")
    print(f"Date range: {processed_df['Date'].min()} to {processed_df['Date'].max()}")
    print(f"Unique exchanges: {processed_df['Exchange'].nunique()}")
    print(f"Unique assets: {processed_df['Asset'].nunique()}")
    
    return processed_df


def get_data_summary(df: pd.DataFrame) -> Dict[str, Any]:
    """Get a summary of the loaded data."""
    return {
        'total_rows': len(df),
        'total_columns': len(df.columns),
        'date_range': {
            'start': str(df['Date'].min()),
            'end': str(df['Date'].max())
        },
        'unique_exchanges': df['Exchange'].nunique(),
        'unique_assets': df['Asset'].nunique(),
        'exchanges': sorted(df['Exchange'].unique().tolist()),
        'assets': sorted(df['Asset'].unique().tolist()),
        'columns': df.columns.tolist()
    }
