"""Analyzer module for processing position data and creating summaries."""

import pandas as pd
import psycopg
import yaml
from datetime import datetime, timedelta
from typing import Dict, Any, List

try:
    from .db import load_and_process_data, load_config
    from .output_generator import PosconExcelGenerator
except ImportError:
    # Handle case when run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from poscon.db import load_and_process_data, load_config
    from poscon.output_generator import PosconExcelGenerator


def analyze_unexplained_by_exchange_and_date(df: pd.DataFrame) -> pd.DataFrame:
    """
    Analyze unexplained modified $ by exchange and date.
    Requires at least 3 dates and excludes the first and last dates from analysis.
    
    Args:
        df: DataFrame with processed position data
        
    Returns:
        DataFrame with Exchange as rows and dates as columns,
        containing sum of 'Unexplained modified $' for each combination
    """
    # Check if required columns exist
    required_columns = ['Exchange', 'Date', 'Unexplained modified $']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    print(f"Analyzing {len(df)} rows of data...")
    print(f"Unique exchanges: {df['Exchange'].nunique()}")
    print(f"Unique dates: {df['Date'].nunique()}")
    print(f"Date range: {df['Date'].min()} to {df['Date'].max()}")
    
    # Check if we have at least 3 dates
    unique_dates = sorted(df['Date'].unique())
    if len(unique_dates) < 3:
        raise ValueError(f"Analysis requires at least 3 dates, but only {len(unique_dates)} dates found: {unique_dates}")
    
    # Exclude first and last dates
    first_date = unique_dates[0]
    last_date = unique_dates[-1]
    middle_dates = unique_dates[1:-1]
    
    print(f"Excluding first date: {first_date}")
    print(f"Excluding last date: {last_date}")
    print(f"Analyzing {len(middle_dates)} middle dates: {middle_dates}")
    
    # Filter data to only include middle dates
    filtered_df = df[df['Date'].isin(middle_dates)].copy()
    print(f"Filtered to {len(filtered_df)} rows after excluding first and last dates")
    
    # Group by Exchange and Date, sum the Unexplained modified $
    grouped = filtered_df.groupby(['Exchange', 'Date'])['Unexplained modified $'].sum().reset_index()
    
    print(f"Grouped data has {len(grouped)} rows")
    
    # Pivot to get dates as columns
    pivot_df = grouped.pivot(index='Exchange', columns='Date', values='Unexplained modified $')
    
    # Fill NaN values with 0 (if an exchange has no data for a particular date)
    pivot_df = pivot_df.fillna(0)
    
    # Reset index to make Exchange a regular column
    pivot_df = pivot_df.reset_index()
    
    # Sort columns: Exchange first, then dates in chronological order
    date_columns = [col for col in pivot_df.columns if col != 'Exchange']
    date_columns_sorted = sorted(date_columns)
    pivot_df = pivot_df[['Exchange'] + date_columns_sorted]
    
    # Round numeric columns to 2 decimal places to avoid scientific notation
    for col in date_columns_sorted:
        pivot_df[col] = pivot_df[col].round(2)
    
    print(f"Final analysis: {len(pivot_df)} exchanges, {len(date_columns_sorted)} date columns")
    
    return pivot_df


def get_analysis_summary(analysis_df: pd.DataFrame) -> Dict[str, Any]:
    """Get summary statistics of the analysis results."""
    # Get date columns (all columns except Exchange)
    date_columns = [col for col in analysis_df.columns if col != 'Exchange']
    
    summary = {
        'total_exchanges': len(analysis_df),
        'date_columns': len(date_columns),
        'dates': [str(date) for date in date_columns],
        'total_unexplained_per_date': {},
        'exchanges_with_unexplained': {},
        'top_exchanges_by_total_unexplained': []
    }
    
    # Calculate totals per date
    for date_col in date_columns:
        total = analysis_df[date_col].sum()
        non_zero_count = (analysis_df[date_col] != 0).sum()
        summary['total_unexplained_per_date'][str(date_col)] = {
            'total': float(total),
            'exchanges_with_data': int(non_zero_count)
        }
    
    # Calculate total unexplained per exchange across all dates
    if date_columns:
        analysis_df['Total_Unexplained'] = analysis_df[date_columns].sum(axis=1)
        
        # Get top 10 exchanges by total unexplained (absolute value)
        top_exchanges = analysis_df.nlargest(10, 'Total_Unexplained')[['Exchange', 'Total_Unexplained']]
        summary['top_exchanges_by_total_unexplained'] = [
            {'exchange': row['Exchange'], 'total_unexplained': float(row['Total_Unexplained'])}
            for _, row in top_exchanges.iterrows()
        ]
    
    return summary


def run_analysis(config_path: str = "config.local.yml") -> tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Run the complete analysis pipeline.
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        Tuple of (analysis_dataframe, summary_dict)
    """
    print("Starting analysis pipeline...")
    print("=" * 50)
    
    # Load and process data
    df = load_and_process_data(config_path)
    
    print("\n" + "=" * 50)
    print("Running unexplained analysis...")
    print("=" * 50)
    
    # Run analysis
    analysis_df = analyze_unexplained_by_exchange_and_date(df)
    
    # Get summary
    summary = get_analysis_summary(analysis_df)
    
    print("\n" + "=" * 50)
    print("Analysis completed!")
    print("=" * 50)
    
    return analysis_df, summary

def identify_problematic_exchanges(analysis_df: pd.DataFrame, threshold: float) -> List[str]:
    """
    Identify exchanges with unexplained amounts exceeding the threshold.
    
    Args:
        analysis_df: DataFrame from analyze_unexplained_by_exchange_and_date
        threshold: Threshold for flagging exchanges (default: $10,000)
        
    Returns:
        List of exchange names that exceed the threshold
    """
    # Get date columns (all columns except Exchange)
    date_columns = [col for col in analysis_df.columns if col != 'Exchange']
    
    problematic_exchanges = []
    
    for _, row in analysis_df.iterrows():
        exchange = row['Exchange']
        
        # Check if any date column has absolute value > threshold
        for date_col in date_columns:
            unexplained_amount = abs(row[date_col])
            if unexplained_amount > threshold:
                problematic_exchanges.append(exchange)
                print(f"Flagged exchange: {exchange} (${row[date_col]:,.2f} on {date_col})")
                break  # Only add each exchange once
    
    return problematic_exchanges


def analyze_problematic_assets(original_df: pd.DataFrame, exchange: str, threshold: float) -> pd.DataFrame:
    """
    Analyze individual assets for a problematic exchange.
    
    Args:
        original_df: Original processed DataFrame from db.py
        exchange: Exchange name to analyze
        threshold: Threshold for flagging individual assets
        
    Returns:
        DataFrame with asset-level analysis for the exchange
    """
    print(f"\nAnalyzing assets for exchange: {exchange}")
    print("-" * 50)
    
    # Filter to the specific exchange
    exchange_data = original_df[original_df['Exchange'] == exchange].copy()
    
    if len(exchange_data) == 0:
        print(f"No data found for exchange: {exchange}")
        return pd.DataFrame()
    
    # Apply the same date filtering as the main analysis
    unique_dates = sorted(exchange_data['Date'].unique())
    if len(unique_dates) < 3:
        print(f"Exchange {exchange} has insufficient dates for analysis: {unique_dates}")
        return pd.DataFrame()
    
    # Filter to middle dates only
    middle_dates = unique_dates[1:-1]
    filtered_data = exchange_data[exchange_data['Date'].isin(middle_dates)].copy()
    
    print(f"Analyzing {len(filtered_data)} rows for {len(middle_dates)} middle dates: {middle_dates}")
    
    # Group by Asset and Date, sum the Unexplained modified $
    asset_analysis = filtered_data.groupby(['Asset', 'Date'])['Unexplained modified $'].sum().reset_index()
    
    # Pivot to get dates as columns
    asset_pivot = asset_analysis.pivot(index='Asset', columns='Date', values='Unexplained modified $')
    asset_pivot = asset_pivot.fillna(0).reset_index()
    
    # Round to 2 decimal places
    date_columns = [col for col in asset_pivot.columns if col != 'Asset']
    for col in date_columns:
        asset_pivot[col] = asset_pivot[col].round(2)
    
    # Calculate total unexplained per asset
    if date_columns:
        asset_pivot['Total_Unexplained'] = asset_pivot[date_columns].sum(axis=1)
    
    # Flag assets that exceed threshold
    asset_pivot['Flagged'] = abs(asset_pivot['Total_Unexplained']) > threshold
    
    # Sort by absolute total unexplained (descending)
    asset_pivot = asset_pivot.reindex(asset_pivot['Total_Unexplained'].abs().sort_values(ascending=False).index)
    
    flagged_assets = asset_pivot[asset_pivot['Flagged']]
    print(f"Found {len(flagged_assets)} assets exceeding ${threshold:,.0f} threshold:")
    
    for _, row in flagged_assets.iterrows():
        asset = row['Asset']
        total = row['Total_Unexplained']
        print(f"  - {asset}: ${total:,.2f}")
    
    return asset_pivot


def get_transaction_data_for_asset(exchange: str, asset: str, date_range: List, config: Dict) -> pd.DataFrame:
    """
    Get transaction data from database for a specific asset using the provided SQL query.
    
    Args:
        exchange: Exchange name
        asset: Asset symbol
        date_range: List of dates to query (middle dates from analysis)
        config: Configuration dictionary with database settings
        
    Returns:
        DataFrame with transaction data from database
    """
    print(f"\nQuerying database for {exchange} - {asset}")
    print(f"Analysis dates: {date_range}")
    
    # Check if asset is a fiat currency (should be ignored)
    fiat_currencies = config.get('fiat_currencies', [])
    if asset in fiat_currencies:
        print(f"Skipping {asset} - fiat currency (not in database)")
        return pd.DataFrame()
    
    # Calculate date range: one day before first date to one day after last date
    if not date_range:
        print("No dates provided")
        return pd.DataFrame()
    
    first_date = min(date_range)
    last_date = max(date_range)
    
    # Convert to datetime and extend range by 1 day on each side
    date_from = datetime.combine(first_date, datetime.min.time()) - timedelta(days=1)
    date_to = datetime.combine(last_date, datetime.max.time().replace(microsecond=0)) + timedelta(days=1)
    
    print(f"Database query range: {date_from} to {date_to}")
    
    # SQL query template
    sql_query = """
    WITH params AS (
        SELECT
            %s::text AS exchange_name,
            %s::text AS asset_name,
            %s::timestamp AS date_from,
            %s::timestamp AS date_to
    )
    SELECT
        t.gt_id,
        t.gt_timestamp,
        sa.gt_exchange  AS source_from_exchange,
        sb.gt_exchange  AS source_to_exchange,
        t.comment,
        t.gt_asset,
        -- Adjust gt_amount based on exchange direction
        CASE
            WHEN sb.gt_exchange = p.exchange_name THEN ABS(t.gt_amount)
            WHEN sa.gt_exchange = p.exchange_name THEN -ABS(t.gt_amount)
            ELSE t.gt_amount
        END                              AS adjusted_gt_amount,
        -- Adjust gt_amount_in_usdt based on exchange direction
        CASE
            WHEN sb.gt_exchange = p.exchange_name THEN ABS(t.gt_amount_in_usdt)
            WHEN sa.gt_exchange = p.exchange_name THEN -ABS(t.gt_amount_in_usdt)
            ELSE t.gt_amount_in_usdt
        END                              AS adjusted_gt_amount_in_usdt,
        t.gt_ts_created,
        t.ts_received,
        t.gt_status_id,
        t.address,
        t.memo,
        t.network,
        t.cycle_id,
        t.external_id,
        t.transaction_id,
        t.linked_transfer_id,
        t.gt_fee,
        t.gt_fee_asset,
        t.gt_fee_in_usdt,
        t.last_updated,
        t.gt_source_author,
        t.gt_source_from,
        t.gt_source_to
    FROM            capman.transfers t
    LEFT JOIN       capman.sources   sb ON t.gt_source_to   = sb.gt_id
    LEFT JOIN       capman.sources   sa ON t.gt_source_from = sa.gt_id
    CROSS JOIN      params           p          
    WHERE       t.gt_asset     = p.asset_name
            AND t.gt_timestamp BETWEEN p.date_from AND p.date_to
            AND (sa.gt_exchange = p.exchange_name OR sb.gt_exchange = p.exchange_name)
    ORDER BY
        t.gt_status_id DESC,
        t.gt_timestamp DESC;
    """
    
    try:
        # Get database configuration
        db_config = config.get('database')
        if not db_config:
            print("No database configuration found")
            return pd.DataFrame()
        
        # Create connection string - handle both 'user' and 'username' keys for flexibility
        username = db_config.get('username') or db_config.get('user')
        if not username:
            print("No username/user found in database config")
            return pd.DataFrame()
            
        conn_str = f"postgresql://{username}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
        
        # Execute query
        with psycopg.connect(conn_str) as conn:
            df = pd.read_sql_query(
                sql_query, 
                conn, 
                params=[exchange, asset, date_from, date_to]
            )
        
        print(f"Retrieved {len(df)} transaction records")
        return df
        
    except Exception as e:
        print(f"Database query failed: {e}")
        # Return empty DataFrame on error
        return pd.DataFrame()


def identify_systematic_explanations(transaction_data: pd.DataFrame, analysis_dates: List, exchange: str, asset: str) -> Dict[str, Any]:
    """
    Identify systematic explanations for unexplained differences based on transfer statuses.
    
    Args:
        transaction_data: DataFrame with transaction data from database
        analysis_dates: List of dates being analyzed (middle dates)
        exchange: Exchange name
        asset: Asset symbol
        
    Returns:
        Dictionary with potential explanations and adjustments
    """
    print(f"\nAnalyzing systematic explanations for {exchange} - {asset}")
    print("-" * 60)
    
    if transaction_data.empty:
        print("No transaction data to analyze")
        return {'adjustments': [], 'total_adjustment_usd': 0.0}
    
    adjustments = []
    total_adjustment_usd = 0.0
    
    # Convert analysis dates to datetime for comparison
    analysis_date_strings = [date.strftime('%Y-%m-%d') for date in analysis_dates]
    print(f"Analysis dates: {analysis_date_strings}")
    
    # Case 1a: Status 200 transfers - handle ts_created vs ts_received timing
    print(f"\n1a. Checking Status 200 transfers with ts_created/ts_received logic...")
    
    status_200_transfers = transaction_data[transaction_data['gt_status_id'] == 200].copy()
    
    case1a_adjustment = 0.0
    case1a_count = 0
    
    if not status_200_transfers.empty:
        # Add date columns for easier comparison
        status_200_transfers['created_date'] = status_200_transfers['gt_ts_created'].dt.date
        status_200_transfers['received_date'] = status_200_transfers['ts_received'].dt.date
        
        for _, transfer in status_200_transfers.iterrows():
            created_date = transfer['created_date']
            received_date = transfer['received_date']
            amount_usd = transfer['adjusted_gt_amount_in_usdt']
            transfer_id = transfer['gt_id']
            
            adjustment_made = False
            reason = ""
            
            # Case 1a.1: ts_created on target date, ts_received on next date -> ADD
            if created_date in analysis_dates:
                # Check if received date is the next day after created date
                next_day = created_date + timedelta(days=1)
                if received_date == next_day:
                    case1a_adjustment -= amount_usd  # Subtract (which adds back to unexplained)
                    adjustment_made = True
                    reason = f"Created {created_date}, received {received_date} (ADD back)"
            
            # Case 1a.2: ts_created on previous date, ts_received on target date -> SUBTRACT
            if received_date in analysis_dates:
                # Check if created date is the previous day before received date
                prev_day = received_date - timedelta(days=1)
                if created_date == prev_day:
                    case1a_adjustment += amount_usd  # Add (which subtracts from unexplained)
                    adjustment_made = True
                    reason = f"Created {created_date}, received {received_date} (SUBTRACT)"
            
            if adjustment_made:
                case1a_count += 1
                print(f"     Transfer {transfer_id}: {reason} (${amount_usd:,.2f})")
        
        if case1a_adjustment != 0:
            total_adjustment_usd += case1a_adjustment
            adjustments.append({
                'case': 'Status 200 transfers with ts_created/ts_received timing',
                'count': case1a_count,
                'adjustment_usd': case1a_adjustment,
                'reason': 'Status 200 transfers crossing date boundaries: created on target+received next day (ADD), created prev+received on target (SUBTRACT)'
            })
            print(f"   Found {case1a_count} relevant status 200 transfers: ${case1a_adjustment:,.2f}")
        else:
            print("   No status 200 transfers found crossing relevant date boundaries")
    else:
        print("   No status 200 transfers found")
    
    # Case 1b: Status 206 transfers (assumed received, no ts_received) on the date of interest
    print(f"\n1b. Checking Status 206 transfers on analysis dates...")
    status_206_on_date = transaction_data[
        (transaction_data['gt_status_id'] == 206) &
        (transaction_data['gt_timestamp'].dt.date.isin(analysis_dates))
    ]
    
    case1b_adjustment = 0.0
    if not status_206_on_date.empty:
        case1b_adjustment = status_206_on_date['adjusted_gt_amount_in_usdt'].sum()
        total_adjustment_usd += case1b_adjustment
        
        adjustments.append({
            'case': 'Status 206 transfers on analysis date',
            'count': len(status_206_on_date),
            'adjustment_usd': case1b_adjustment,
            'reason': 'Status 206 transfers (assumed received) on analysis date should be subtracted from unexplained'
        })
        print(f"   Found {len(status_206_on_date)} status 206 transfers: ${case1b_adjustment:,.2f}")
        print(f"   Note: Status 206 transfers are assumed to be received (no ts_received timestamp)")
        
        # Show individual transfers for debugging
        for _, transfer in status_206_on_date.iterrows():
            print(f"     Transfer {transfer['gt_id']}: Status 206, "
                  f"{transfer['gt_timestamp'].date()}, ${transfer['adjusted_gt_amount_in_usdt']:,.2f}")
    else:
        print("   No status 206 transfers found on analysis dates")
    
    # Case 2: Status 100-109 transfers that started on the day of interest
    print(f"\n2. Checking Status 100-109 transfers started on analysis dates...")
    status_100_109_started = transaction_data[
        (transaction_data['gt_status_id'].between(100, 109)) &
        (transaction_data['gt_ts_created'].dt.date.isin(analysis_dates))
    ]
    
    if not status_100_109_started.empty:
        case2_adjustment = status_100_109_started['adjusted_gt_amount_in_usdt'].sum()
        total_adjustment_usd -= case2_adjustment
        adjustments.append({
            'case': 'Status 100-109 started on analysis date',
            'count': len(status_100_109_started),
            'adjustment_usd': case2_adjustment,
            'reason': 'Pending/in-progress transfers started on analysis date should be subtracted'
        })
        print(f"   Found {len(status_100_109_started)} status 100-109 transfers: ${case2_adjustment:,.2f}")
    else:
        print("   No status 100-109 transfers found starting on analysis dates")
    
    # Case 3: Complex case - transfers that started on one day and ended on another
    print(f"\n3. Checking transfers spanning multiple days...")
    
    # Identify transfers where created date != timestamp date
    transaction_data['created_date'] = transaction_data['gt_ts_created'].dt.date
    transaction_data['timestamp_date'] = transaction_data['gt_timestamp'].dt.date
    
    spanning_transfers = transaction_data[
        transaction_data['created_date'] != transaction_data['timestamp_date']
    ]
    
    if not spanning_transfers.empty:
        print(f"   Found {len(spanning_transfers)} transfers spanning multiple days")
        
        case3_adjustment = 0.0
        for _, transfer in spanning_transfers.iterrows():
            created_date = transfer['created_date']
            completed_date = transfer['timestamp_date']
            amount_usd = transfer['adjusted_gt_amount_in_usdt']
            status = transfer['gt_status_id']
            
            # Determine if we need to adjust based on where the transfer was during analysis
            created_in_analysis = created_date in analysis_dates
            completed_in_analysis = completed_date in analysis_dates
            
            adjustment_reason = ""
            if created_in_analysis and not completed_in_analysis:
                # Transfer started in analysis period but completed outside
                if status >= 200:  # Completed status
                    # This should be added back (transfer left our analysis period)
                    case3_adjustment -= amount_usd  # Negative because we add it back
                    adjustment_reason = f"Started {created_date}, completed {completed_date} (add back)"
                else:
                    # Still pending, should be subtracted
                    case3_adjustment += amount_usd
                    adjustment_reason = f"Started {created_date}, still pending (subtract)"
            
            elif not created_in_analysis and completed_in_analysis:
                # Transfer started outside analysis period but completed inside
                if status >= 200:  # Completed status
                    # This should be subtracted (transfer entered our analysis period)
                    case3_adjustment += amount_usd
                    adjustment_reason = f"Started {created_date}, completed {completed_date} (subtract)"
            
            if adjustment_reason:
                print(f"     Transfer {transfer['gt_id']}: {adjustment_reason} (${amount_usd:,.2f})")
        
        if case3_adjustment != 0:
            total_adjustment_usd += case3_adjustment
            adjustments.append({
                'case': 'Transfers spanning multiple days',
                'count': len(spanning_transfers),
                'adjustment_usd': case3_adjustment,
                'reason': 'Transfers starting/ending outside analysis period need directional adjustment'
            })
            print(f"   Total case 3 adjustment: ${case3_adjustment:,.2f}")
    else:
        print("   No transfers spanning multiple days found")
    
    # Summary
    print(f"\n{'='*60}")
    print(f"SYSTEMATIC EXPLANATION SUMMARY")
    print(f"{'='*60}")
    print(f"Total adjustments found: {len(adjustments)}")
    print(f"Total adjustment amount: ${total_adjustment_usd:,.2f}")
    
    if adjustments:
        print(f"\nBreakdown:")
        for adj in adjustments:
            print(f"  - {adj['case']}: {adj['count']} transfers, ${adj['adjustment_usd']:,.2f}")
            print(f"    Reason: {adj['reason']}")
    
    return {
        'adjustments': adjustments,
        'total_adjustment_usd': total_adjustment_usd,
        'exchange': exchange,
        'asset': asset,
        'analysis_dates': analysis_date_strings,
        'total_transactions_analyzed': len(transaction_data)
    }


def run_deep_analysis(config_path: str = "config.local.yml", threshold: float = None) -> Dict[str, Any]:
    """
    Run the complete deep analysis pipeline for problematic exchanges and assets.
    
    Args:
        config_path: Path to configuration file
        threshold: Threshold for flagging exchanges and assets (uses config if None)
        
    Returns:
        Dictionary with analysis results
    """
    print("Starting deep analysis pipeline...")
    print("=" * 60)
    
    # Load configuration
    config = load_config(config_path)
    
    # Use threshold from config if not provided
    if threshold is None:
        threshold = config.get('processing', {}).get('threshold', 10000.0)
    
    print(f"Threshold: ${threshold:,.0f}")
    
    # Load and process data
    df = load_and_process_data(config_path)
    
    # Run main analysis
    print("\n" + "=" * 60)
    print("Running main exchange analysis...")
    print("=" * 60)
    analysis_df = analyze_unexplained_by_exchange_and_date(df)
    
    # Identify problematic exchanges
    print("\n" + "=" * 60)
    print("Identifying problematic exchanges...")
    print("=" * 60)
    problematic_exchanges = identify_problematic_exchanges(analysis_df, threshold)
    
    if not problematic_exchanges:
        print(f"No exchanges found with unexplained amounts > ${threshold:,.0f}")
        return {'problematic_exchanges': [], 'analysis_complete': True}
    
    results = {
        'threshold': threshold,
        'problematic_exchanges': problematic_exchanges,
        'exchange_details': {},
        'flagged_assets': {},
        'explanations': {}
    }
    
    # Analyze each problematic exchange
    for exchange in problematic_exchanges:
        print(f"\n{'='*60}")
        print(f"Deep dive into exchange: {exchange}")
        print(f"{'='*60}")
        
        # Get asset-level analysis
        asset_analysis = analyze_problematic_assets(df, exchange, threshold)
        results['exchange_details'][exchange] = asset_analysis
        
        # Get flagged assets for this exchange
        if not asset_analysis.empty:
            flagged_assets = asset_analysis[asset_analysis['Flagged']]['Asset'].tolist()
            results['flagged_assets'][exchange] = flagged_assets
            
            # For each flagged asset, prepare for transaction data query
            unique_dates = sorted(df['Date'].unique())
            middle_dates = unique_dates[1:-1] if len(unique_dates) >= 3 else unique_dates
            
            for asset in flagged_assets:  # Process all flagged assets
                print(f"\n--- Analyzing {asset} ---")
                
                # Get transaction data from database
                transaction_data = get_transaction_data_for_asset(exchange, asset, middle_dates, config)
                
                # Analyze systematic explanations
                explanations = identify_systematic_explanations(transaction_data, middle_dates, exchange, asset)
                
                # Store explanations in results
                if exchange not in results.get('explanations', {}):
                    results.setdefault('explanations', {})[exchange] = {}
                results['explanations'][exchange][asset] = explanations
    
    print(f"\n{'='*60}")
    print("Deep analysis completed!")
    print(f"{'='*60}")
    print(f"Problematic exchanges found: {len(problematic_exchanges)}")
    
    return results


def generate_excel_report(analysis_results: Dict[str, Any], config: Dict) -> str:
    """
    Generate Excel report from deep analysis results.
    
    Args:
        analysis_results: Results from run_deep_analysis()
        config: Configuration dictionary
        
    Returns:
        Path to generated Excel file
    """
    # Get output path from config
    output_path = config.get('output_excel_path', 'output/poscon_report.xlsx')
    
    # Add timestamp to filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if output_path.endswith('.xlsx'):
        output_path = output_path.replace('.xlsx', f'_{timestamp}.xlsx')
    else:
        output_path = f"{output_path}_{timestamp}.xlsx"
    
    print(f"\n{'='*60}")
    print("GENERATING EXCEL REPORT")
    print(f"{'='*60}")
    
    # Create Excel generator and generate report
    excel_generator = PosconExcelGenerator(output_path)
    generated_path = excel_generator.generate_poscon_report(analysis_results)
    
    return generated_path


def run_complete_analysis(config_path: str = "config.local.yml", threshold: float = None, generate_excel: bool = True) -> Dict[str, Any]:
    """
    Run complete POSCON analysis including Excel report generation.
    
    Args:
        config_path: Path to configuration file
        threshold: Threshold for flagging exchanges and assets (uses config if None)
        generate_excel: Whether to generate Excel report
        
    Returns:
        Dictionary with analysis results and Excel path
    """
    print("Starting Complete POSCON Analysis...")
    print("=" * 70)
    
    # Load configuration
    config = load_config(config_path)
    
    # Run deep analysis
    analysis_results = run_deep_analysis(config_path, threshold)
    
    # Generate Excel report if requested
    excel_path = None
    if generate_excel:
        excel_path = generate_excel_report(analysis_results, config)
    
    # Add Excel path to results
    analysis_results['excel_report_path'] = excel_path
    
    print(f"\n{'='*70}")
    print("COMPLETE ANALYSIS FINISHED")
    print(f"{'='*70}")
    
    if excel_path:
        print(f"Excel report generated: {excel_path}")
    
    return analysis_results

if __name__ == "__main__":
    # Test the complete analyzer with Excel generation
    try:
        # Run complete analysis (includes deep analysis + Excel generation)
        complete_results = run_complete_analysis()
        
        print(f"\n{'='*70}")
        print("ANALYSIS SUMMARY")
        print(f"{'='*70}")
        
        print(f"Threshold used: ${complete_results.get('threshold', 'N/A'):,.0f}")
        print(f"Problematic exchanges found: {len(complete_results.get('problematic_exchanges', []))}")
        
        if complete_results.get('explanations'):
            total_explanations = sum(
                len(assets) for assets in complete_results['explanations'].values()
            )
            print(f"Assets analyzed for systematic explanations: {total_explanations}")
        
        if complete_results.get('excel_report_path'):
            print(f"Excel report generated: {complete_results['excel_report_path']}")
        
        print(f"\nProblematic exchanges:")
        for i, exchange in enumerate(complete_results.get('problematic_exchanges', []), 1):
            print(f"  {i:2d}. {exchange}")
        
    except Exception as e:
        print(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()
